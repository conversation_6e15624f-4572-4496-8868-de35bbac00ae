import React, { memo } from 'react';
import { Navigation } from '@/components/Navigation';
import { Footer } from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ServicePageTemplateProps {
  // Hero Section
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  heroImage?: string;
  heroImageAlt?: string;

  // Problem Statement Section
  problemTitle: string;
  problemDescription: string;
  problemPoints: string[];

  // Solution Section
  solutionTitle: string;
  solutionDescription: string;
  solutionFeatures: Array<{
    title: string;
    description: string;
    icon?: React.ReactNode;
  }>;

  // Proof/Evidence Section
  proofTitle: string;
  proofDescription: string;
  proofItems: Array<{
    metric: string;
    description: string;
    icon?: React.ReactNode;
  }>;

  // CTA Section
  ctaTitle: string;
  ctaDescription: string;
  ctaButtonText: string;
  ctaButtonLink?: string;
}

export const ServicePageTemplate = memo(function ServicePageTemplate({
  heroTitle,
  heroSubtitle,
  heroDescription,
  heroImage,
  heroImageAlt,
  problemTitle,
  problemDescription,
  problemPoints,
  solutionTitle,
  solutionDescription,
  solutionFeatures,
  proofTitle,
  proofDescription,
  proofItems,
  ctaTitle,
  ctaDescription,
  ctaButtonText,
  ctaButtonLink = "/contact"
}: ServicePageTemplateProps) {
  const baseUrl = import.meta.env.BASE_URL || '/';
  const withBase = (path: string) => {
    const base = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
    return `${base}${path.replace(/^\/+/, '')}`;
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <main role="main" id="main-content" className="font-sans antialiased">

        {/* Hero Section */}
        <section className="bg-gradient-to-b from-[#FEFEFE] to-[#FEFEFE] py-16 md:py-20 lg:py-24 xl:py-28" aria-labelledby="service-hero-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">

              {/* Left Column - Content */}
              <div className="space-y-6">
                <header>
                  <div className="mb-4">
                    <span className="inline-block px-4 py-2 bg-ethos-purple/10 text-ethos-purple text-sm font-medium rounded-full">
                      {heroSubtitle}
                    </span>
                  </div>
                  <h1 id="service-hero-heading" className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-7.5xl font-semibold font-poppins leading-tight tracking-[-0.02em]">
                    <span className="bg-gradient-to-r from-ethos-navy-light to-ethos-gray-light bg-clip-text text-transparent">
                      {heroTitle}
                    </span>
                  </h1>
                </header>
                <p className="text-ethos-gray text-base sm:text-lg lg:text-xl font-light leading-relaxed tracking-normal max-w-2xl">
                  {heroDescription}
                </p>
                <div className="pt-4">
                  <Link to={ctaButtonLink}>
                    <Button
                      variant="cta"
                      size="cta"
                      className="group relative overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
                    >
                      <span className="relative z-10 flex items-center">
                        Get Started Today
                        <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                      </span>
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Right Column - Image */}
              {heroImage && (
                <div className="relative">
                  <img
                    src={withBase(heroImage)}
                    alt={heroImageAlt || "Service illustration"}
                    className="w-full h-auto max-w-lg mx-auto"
                    loading="eager"
                    fetchPriority="high"
                  />
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Problem Statement Section */}
        <section className="py-16 md:py-20 lg:py-24" style={{ background: 'linear-gradient(180deg, #FFF 37.11%, #F2F2F2 100%)' }} aria-labelledby="problem-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              <header className="mb-12">
                <h2 id="problem-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
                  <span className="text-ethos-navy">{problemTitle}</span>
                </h2>
                <p className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed">
                  {problemDescription}
                </p>
              </header>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
                {problemPoints.map((point, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 text-red-500 mr-3 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                      </div>
                      <p className="text-gray-700 text-sm leading-relaxed">{point}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Solution Section */}
        <section className="py-16 md:py-20 lg:py-24 bg-white" aria-labelledby="solution-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="solution-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
                <span className="text-ethos-navy">Our </span>
                <span className="text-ethos-purple">Solution</span>
              </h2>
              <p className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed max-w-4xl mx-auto">
                {solutionDescription}
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {solutionFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-b from-gray-50 to-gray-100 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  {feature.icon && (
                    <div className="w-12 h-12 bg-ethos-purple/10 rounded-lg flex items-center justify-center mb-4 text-ethos-purple">
                      {feature.icon}
                    </div>
                  )}
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Proof/Evidence Section */}
        <section className="py-16 md:py-20 lg:py-24" style={{ background: 'linear-gradient(180deg, #FFF 60.69%, #DDD 100%)' }} aria-labelledby="proof-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="proof-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
                <span className="text-ethos-navy">{proofTitle}</span>
              </h2>
              <p className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed max-w-4xl mx-auto">
                {proofDescription}
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {proofItems.map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  {item.icon && (
                    <div className="w-16 h-16 bg-ethos-purple/10 rounded-full flex items-center justify-center mx-auto mb-4 text-ethos-purple">
                      {item.icon}
                    </div>
                  )}
                  <div className="text-4xl font-bold text-ethos-purple mb-2">{item.metric}</div>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Call-to-Action Section */}
        <section className="py-16 md:py-20 lg:py-24 bg-white" aria-labelledby="cta-heading">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <header className="mb-8">
              <h2 id="cta-heading" className="text-3xl sm:text-4xl md:text-5xl font-medium leading-tight tracking-[-0.02em] mb-6">
                <span className="text-ethos-navy">{ctaTitle}</span>
              </h2>
              <p className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed">
                {ctaDescription}
              </p>
            </header>

            <Link to={ctaButtonLink}>
              <Button
                variant="cta"
                size="cta"
                className="group relative overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
              >
                <span className="relative z-10 flex items-center">
                  {ctaButtonText}
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
              </Button>
            </Link>
          </div>
        </section>

      </main>
      <Footer />
    </div>
  );
});