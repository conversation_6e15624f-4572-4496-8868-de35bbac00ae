import React, { memo } from 'react';
import { Navigation } from '@/components/Navigation';
import { Footer } from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import ShinyText from '@/components/ShinyText';

interface ServicePageTemplateProps {
  // Hero Section
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  heroImage?: string;
  heroImageAlt?: string;

  // Problem Statement Section
  problemTitle: string;
  problemDescription: string;
  problemPoints: string[];

  // Solution Section
  solutionTitle: string;
  solutionDescription: string;
  solutionFeatures: Array<{
    title: string;
    description: string;
    icon?: React.ReactNode;
  }>;

  // Proof/Evidence Section
  proofTitle: string;
  proofDescription: string;
  proofItems: Array<{
    metric: string;
    description: string;
    icon?: React.ReactNode;
  }>;

  // CTA Section
  ctaTitle: string;
  ctaDescription: string;
  ctaButtonText: string;
  ctaButtonLink?: string;
}

export const ServicePageTemplate = memo(function ServicePageTemplate({
  heroTitle,
  heroSubtitle,
  heroDescription,
  heroImage,
  heroImageAlt,
  problemTitle,
  problemDescription,
  problemPoints,
  solutionTitle,
  solutionDescription,
  solutionFeatures,
  proofTitle,
  proofDescription,
  proofItems,
  ctaTitle,
  ctaDescription,
  ctaButtonText,
  ctaButtonLink = "/contact"
}: ServicePageTemplateProps) {
  const baseUrl = import.meta.env.BASE_URL || '/';
  const withBase = (path: string) => {
    const base = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
    return `${base}${path.replace(/^\/+/, '')}`;
  };

  // Enhanced animation styles matching home page aesthetic
  const enhancedAnimationStyle = `
    @keyframes float-enhanced {
      0% { transform: translateY(0) scale(1); }
      50% { transform: translateY(-8px) scale(1.02); }
      100% { transform: translateY(0) scale(1); }
    }
    .animate-float-enhanced {
      animation: float-enhanced 6s ease-in-out infinite;
    }

    @keyframes pulse-glow {
      0%, 100% { box-shadow: 0 0 20px rgba(116, 9, 197, 0.3); }
      50% { box-shadow: 0 0 40px rgba(116, 9, 197, 0.6); }
    }
    .animate-pulse-glow {
      animation: pulse-glow 3s ease-in-out infinite;
    }

    @keyframes gradient-shift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
    .animate-gradient-shift {
      background-size: 200% 200%;
      animation: gradient-shift 8s ease infinite;
    }

    @keyframes star-border-enhanced {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .animate-star-border-enhanced {
      position: relative;
      z-index: 0;
      overflow: hidden;
    }
    .animate-star-border-enhanced::before {
      content: '';
      position: absolute;
      z-index: -1;
      inset: -2px;
      background: conic-gradient(
        from 0deg,
        transparent,
        rgba(116, 9, 197, 0.4),
        rgba(139, 92, 246, 0.6),
        rgba(168, 85, 247, 0.8),
        rgba(192, 132, 252, 0.6),
        rgba(116, 9, 197, 0.4),
        transparent 180deg,
        rgba(116, 9, 197, 0.4),
        rgba(139, 92, 246, 0.6),
        rgba(168, 85, 247, 0.8),
        rgba(192, 132, 252, 0.6),
        rgba(116, 9, 197, 0.4),
        transparent 360deg
      );
      border-radius: inherit;
      animation: star-border-enhanced 12s linear infinite;
      opacity: 0.8;
    }
    .animate-star-border-enhanced::after {
      content: '';
      position: absolute;
      z-index: -1;
      inset: 2px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: inherit;
    }
  `;

  return (
    <div className="min-h-screen bg-white">
      <style dangerouslySetInnerHTML={{ __html: enhancedAnimationStyle }} />
      <Navigation />
      <main role="main" id="main-content" className="font-sans antialiased">

        {/* Hero Section */}
        <section className="relative bg-gradient-to-b from-[#FEFEFE] via-[#FAFAFA] to-[#F5F5F5] py-16 md:py-20 lg:py-24 xl:py-28 overflow-hidden" aria-labelledby="service-hero-heading">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-r from-ethos-purple/20 via-transparent to-ethos-purple/20 animate-gradient-shift"></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >

              {/* Left Column - Content */}
              <div className="space-y-8">
                <motion.header
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <div className="mb-6">
                    <span className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-ethos-purple/10 to-ethos-purple/5 text-ethos-purple text-sm font-medium rounded-full border border-ethos-purple/20 animate-pulse-glow">
                      <span className="w-2 h-2 bg-ethos-purple rounded-full mr-2 animate-pulse"></span>
                      {heroSubtitle}
                    </span>
                  </div>
                  <h1 id="service-hero-heading" className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-semibold font-poppins leading-tight tracking-[-0.02em]">
                    <span className="bg-gradient-to-r from-ethos-navy-light to-ethos-gray-light bg-clip-text text-transparent">
                      {heroTitle.split(' ').slice(0, -1).join(' ')}{' '}
                    </span>
                    <ShinyText className="bg-gradient-to-r from-ethos-purple to-ethos-purple-light" speedInMs={8000}>
                      {heroTitle.split(' ').slice(-1)[0]}
                    </ShinyText>
                  </h1>
                </motion.header>

                <motion.p
                  className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed tracking-normal max-w-2xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  {heroDescription}
                </motion.p>

                <motion.div
                  className="pt-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <Link to={ctaButtonLink}>
                    <Button
                      variant="cta"
                      size="cta"
                      className="group relative overflow-hidden transition-all duration-500 hover:scale-[1.05] hover:shadow-2xl animate-star-border-enhanced"
                    >
                      <span className="relative z-10 flex items-center text-lg font-medium">
                        Transform Your Business
                        <ArrowRight className="ml-3 h-6 w-6 transition-all duration-300 group-hover:translate-x-2 group-hover:scale-110" />
                      </span>
                      <div className="absolute inset-0 bg-gradient-to-r from-ethos-purple via-ethos-purple-light to-ethos-purple opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </Button>
                  </Link>
                </motion.div>
              </div>

              {/* Right Column - Enhanced Image */}
              {heroImage && (
                <motion.div
                  className="relative"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 1, delay: 0.3 }}
                >
                  <div className="relative animate-float-enhanced">
                    <div className="absolute inset-0 bg-gradient-to-r from-ethos-purple/20 to-ethos-purple-light/20 rounded-3xl blur-3xl transform scale-110"></div>
                    <img
                      src={withBase(heroImage)}
                      alt={heroImageAlt || "Service illustration"}
                      className="relative w-full h-auto max-w-lg mx-auto rounded-2xl shadow-2xl"
                      loading="eager"
                      fetchPriority="high"
                    />
                  </div>
                </motion.div>
              )}
            </motion.div>
          </div>
        </section>

        {/* Problem Statement Section */}
        <section className="py-16 md:py-20 lg:py-24 relative overflow-hidden" style={{ background: 'linear-gradient(180deg, #FFF 37.11%, #F2F2F2 100%)' }} aria-labelledby="problem-heading">
          {/* Background Elements */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-orange-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              className="max-w-4xl mx-auto text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <header className="mb-12">
                <h2 id="problem-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
                  <span className="bg-gradient-to-r from-red-600 via-orange-600 to-red-700 bg-clip-text text-transparent">
                    The Challenge:{' '}
                  </span>
                  <span className="text-ethos-navy">
                    {problemTitle.replace('The Challenge of ', '').replace('The Challenge: ', '')}
                  </span>
                </h2>
                <motion.p
                  className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  {problemDescription}
                </motion.p>
              </header>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
                {problemPoints.map((point, index) => (
                  <motion.div
                    key={index}
                    className="group bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-red-100/50 hover:shadow-2xl hover:border-red-200 transition-all duration-500 animate-star-border-enhanced"
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    whileInView={{ opacity: 1, y: 0, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-red-100 to-orange-100 rounded-full flex items-center justify-center mr-3 mt-1 group-hover:scale-110 transition-transform duration-300">
                        <div className="w-3 h-3 bg-gradient-to-r from-red-500 to-orange-500 rounded-full animate-pulse"></div>
                      </div>
                      <p className="text-gray-700 text-sm leading-relaxed group-hover:text-ethos-navy transition-colors duration-300">{point}</p>
                    </div>

                    {/* Hover Effect Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-r from-red-50/50 to-orange-50/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                  </motion.div>
                ))}
              </div>

              {/* Impact Statement */}
              <motion.div
                className="mt-16"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-50 to-orange-50 rounded-full border border-red-200/50">
                  <span className="text-red-600 font-medium text-lg">
                    These challenges cost businesses millions in lost opportunities and inefficiencies
                  </span>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Solution Section */}
        <section className="py-16 md:py-20 lg:py-24 bg-gradient-to-b from-white via-gray-50/30 to-white relative overflow-hidden" aria-labelledby="solution-heading">
          {/* Background Elements */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-ethos-purple/30 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-1/3 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.header
              className="text-center mb-16"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 id="solution-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
                <span className="text-ethos-navy">Our </span>
                <ShinyText className="bg-gradient-to-r from-ethos-purple to-ethos-purple-light" speedInMs={6000}>
                  Solution
                </ShinyText>
              </h2>
              <motion.p
                className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed max-w-4xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                {solutionDescription}
              </motion.p>
            </motion.header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {solutionFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  className="group bg-gradient-to-br from-white via-gray-50/50 to-white rounded-2xl p-8 shadow-lg border border-ethos-purple/10 hover:shadow-2xl hover:border-ethos-purple/30 transition-all duration-500 animate-star-border-enhanced animate-float-enhanced"
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.15 }}
                  whileHover={{ y: -10, scale: 1.02 }}
                  style={{ animationDelay: `${index * 0.5}s` }}
                >
                  {feature.icon && (
                    <div className="w-16 h-16 bg-gradient-to-br from-ethos-purple/20 to-ethos-purple-light/20 rounded-2xl flex items-center justify-center mb-6 text-ethos-purple group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 animate-pulse-glow">
                      <div className="transform group-hover:scale-125 transition-transform duration-300">
                        {feature.icon}
                      </div>
                    </div>
                  )}
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-ethos-navy to-ethos-gray bg-clip-text text-transparent mb-4 group-hover:from-ethos-purple group-hover:to-ethos-purple-light transition-all duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed group-hover:text-gray-800 transition-colors duration-300">
                    {feature.description}
                  </p>

                  {/* Hover Effect Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-ethos-purple/5 to-blue-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                </motion.div>
              ))}
            </div>

            {/* Solution Benefits Summary */}
            <motion.div
              className="mt-20 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center px-10 py-5 bg-gradient-to-r from-ethos-purple/10 to-blue-500/10 rounded-full border border-ethos-purple/20 animate-pulse-glow">
                <span className="text-ethos-purple font-semibold text-xl">
                  ✨ Transform your business with intelligent automation that works 24/7
                </span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Proof/Evidence Section */}
        <section className="py-16 md:py-20 lg:py-24" style={{ background: 'linear-gradient(180deg, #FFF 60.69%, #DDD 100%)' }} aria-labelledby="proof-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="proof-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
                <span className="text-ethos-navy">{proofTitle}</span>
              </h2>
              <p className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed max-w-4xl mx-auto">
                {proofDescription}
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {proofItems.map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  {item.icon && (
                    <div className="w-16 h-16 bg-ethos-purple/10 rounded-full flex items-center justify-center mx-auto mb-4 text-ethos-purple">
                      {item.icon}
                    </div>
                  )}
                  <div className="text-4xl font-bold text-ethos-purple mb-2">{item.metric}</div>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Call-to-Action Section */}
        <section className="py-16 md:py-20 lg:py-24 bg-white" aria-labelledby="cta-heading">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <header className="mb-8">
              <h2 id="cta-heading" className="text-3xl sm:text-4xl md:text-5xl font-medium leading-tight tracking-[-0.02em] mb-6">
                <span className="text-ethos-navy">{ctaTitle}</span>
              </h2>
              <p className="text-ethos-gray text-lg sm:text-xl lg:text-2xl font-light leading-relaxed">
                {ctaDescription}
              </p>
            </header>

            <Link to={ctaButtonLink}>
              <Button
                variant="cta"
                size="cta"
                className="group relative overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
              >
                <span className="relative z-10 flex items-center">
                  {ctaButtonText}
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
              </Button>
            </Link>
          </div>
        </section>

      </main>
      <Footer />
    </div>
  );
});