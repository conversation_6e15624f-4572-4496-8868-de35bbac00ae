import React from 'react';
import { ServicePageTemplate } from '@/components/templates/ServicePageTemplate';
import { Bot, MessageSquare, TrendingUp, Users, Zap, Shield, Clock, Target } from 'lucide-react';

export default function CustomAISolutions() {
  return (
    <ServicePageTemplate
      // Hero Section
      heroTitle="Custom AI Solutions"
      heroSubtitle="AI-Powered Business Automation"
      heroDescription="Transform your business operations with tailored AI systems designed specifically for customer support, sales optimization, and operational automation. Our intelligent solutions work around the clock to deliver exceptional results while reducing costs and improving efficiency."
      heroImage="assets/images/ai-workflow-illustration.jpg"
      heroImageAlt="Custom AI Solutions workflow illustration"

      // Problem Statement Section
      problemTitle="The Challenge of Manual Operations"
      problemDescription="Businesses today struggle with inefficient manual processes that limit growth, increase costs, and create inconsistent customer experiences."
      problemPoints={[
        "Customer support teams overwhelmed with repetitive inquiries, leading to long response times and frustrated customers",
        "Sales teams spending 60% of their time on administrative tasks instead of building relationships and closing deals",
        "Inconsistent service quality due to human limitations, varying skill levels, and staff turnover",
        "High operational costs from hiring, training, and maintaining large support and sales teams",
        "Limited availability - traditional teams can't provide 24/7 coverage without significant expense",
        "Difficulty scaling operations quickly during peak periods or business growth phases"
      ]}

      // Solution Section
      solutionTitle="Our Custom AI Solutions"
      solutionDescription="We develop intelligent AI systems tailored to your specific business needs, automating complex workflows while maintaining the personal touch your customers expect."
      solutionFeatures={[
        {
          title: "Intelligent Customer Support",
          description: "AI-powered support systems that understand context, resolve complex issues, and escalate appropriately while maintaining your brand voice and values.",
          icon: <MessageSquare className="w-6 h-6" />
        },
        {
          title: "Sales Process Automation",
          description: "Automated lead qualification, follow-up sequences, and sales pipeline management that increases conversion rates and frees your team for high-value activities.",
          icon: <TrendingUp className="w-6 h-6" />
        },
        {
          title: "Operational Intelligence",
          description: "Smart workflow automation that handles routine tasks, monitors performance, and provides actionable insights for continuous improvement.",
          icon: <Zap className="w-6 h-6" />
        },
        {
          title: "Personalized Interactions",
          description: "AI that learns from each customer interaction to provide increasingly personalized and effective responses over time.",
          icon: <Users className="w-6 h-6" />
        },
        {
          title: "24/7 Availability",
          description: "Round-the-clock service that never sleeps, ensuring your customers receive immediate assistance whenever they need it.",
          icon: <Clock className="w-6 h-6" />
        },
        {
          title: "Enterprise Security",
          description: "Bank-level security protocols and compliance standards to protect your data and maintain customer trust.",
          icon: <Shield className="w-6 h-6" />
        }
      ]}

      // Proof/Evidence Section
      proofTitle="Proven Results"
      proofDescription="Our custom AI solutions deliver measurable improvements across key business metrics, helping companies achieve sustainable growth and operational excellence."
      proofItems={[
        {
          metric: "85%",
          description: "Reduction in customer support response time, leading to higher satisfaction scores",
          icon: <Clock className="w-8 h-8" />
        },
        {
          metric: "60%",
          description: "Increase in sales team productivity by automating administrative tasks",
          icon: <TrendingUp className="w-8 h-8" />
        },
        {
          metric: "40%",
          description: "Lower operational costs through intelligent automation and process optimization",
          icon: <Target className="w-8 h-8" />
        }
      ]}

      // CTA Section
      ctaTitle="Ready to Transform Your Business?"
      ctaDescription="Join hundreds of companies that have revolutionized their operations with our custom AI solutions. Let's discuss how we can tailor the perfect AI system for your specific needs."
      ctaButtonText="Schedule Your AI Consultation"
      ctaButtonLink="/contact"
    />
  );
}