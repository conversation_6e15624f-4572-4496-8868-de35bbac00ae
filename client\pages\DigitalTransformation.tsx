import React from 'react';
import { ServicePageTemplate } from '@/components/templates/ServicePageTemplate';
import { Rocket, Globe, Cpu, Users, TrendingUp, Shield, Lightbulb, RefreshCw } from 'lucide-react';

export default function DigitalTransformation() {
  return (
    <ServicePageTemplate
      // Hero Section
      heroTitle="Digital Transformation"
      heroSubtitle="Future-Ready Business Evolution"
      heroDescription="Modernize your digital presence with intelligent automation and enhanced user experiences. Our comprehensive digital transformation solutions future-proof your business with cutting-edge technology and strategic innovation."
      heroImage="assets/images/Group hero.png"
      heroImageAlt="Digital transformation and modernization illustration"

      // Problem Statement Section
      problemTitle="The Digital Modernization Gap"
      problemDescription="Many businesses struggle with outdated systems, manual processes, and disconnected digital experiences that limit growth and competitiveness."
      problemPoints={[
        "Legacy systems that can't scale with business growth or adapt to changing market demands",
        "Fragmented digital experiences that confuse customers and reduce conversion rates",
        "Manual processes that waste time, increase errors, and prevent teams from focusing on strategic work",
        "Lack of data-driven insights preventing informed decision-making and strategic planning",
        "Outdated technology stack that increases security risks and maintenance costs",
        "Inability to compete with digitally-native companies that leverage modern technology advantages"
      ]}

      // Solution Section
      solutionTitle="Our Transformation Approach"
      solutionDescription="We guide your business through comprehensive digital transformation, modernizing systems, processes, and experiences to create competitive advantages and sustainable growth."
      solutionFeatures={[
        {
          title: "Strategic Digital Planning",
          description: "Comprehensive assessment and roadmap development to align technology initiatives with business objectives and market opportunities.",
          icon: <Lightbulb className="w-6 h-6" />
        },
        {
          title: "Modern Technology Stack",
          description: "Implementation of cutting-edge technologies including AI, cloud computing, and automation to create scalable, efficient systems.",
          icon: <Cpu className="w-6 h-6" />
        },
        {
          title: "Process Automation",
          description: "Intelligent automation of manual processes to improve efficiency, reduce errors, and free your team for high-value strategic work.",
          icon: <RefreshCw className="w-6 h-6" />
        },
        {
          title: "Enhanced User Experience",
          description: "Complete redesign of customer touchpoints to create engaging, intuitive experiences that drive satisfaction and loyalty.",
          icon: <Users className="w-6 h-6" />
        },
        {
          title: "Data-Driven Insights",
          description: "Implementation of analytics and business intelligence systems that provide actionable insights for strategic decision-making.",
          icon: <TrendingUp className="w-6 h-6" />
        },
        {
          title: "Security & Compliance",
          description: "Modern security frameworks and compliance protocols that protect your business and customer data in the digital age.",
          icon: <Shield className="w-6 h-6" />
        }
      ]}

      // Proof/Evidence Section
      proofTitle="Transformation Success Metrics"
      proofDescription="Our digital transformation initiatives consistently deliver measurable improvements in efficiency, customer satisfaction, and business growth."
      proofItems={[
        {
          metric: "200%",
          description: "Average increase in operational efficiency through intelligent automation and modern systems",
          icon: <Rocket className="w-8 h-8" />
        },
        {
          metric: "95%",
          description: "Improvement in customer satisfaction scores through enhanced digital experiences",
          icon: <Users className="w-8 h-8" />
        },
        {
          metric: "70%",
          description: "Reduction in manual processes and administrative overhead through strategic automation",
          icon: <RefreshCw className="w-8 h-8" />
        }
      ]}

      // CTA Section
      ctaTitle="Ready to Transform Your Business?"
      ctaDescription="Join the digital revolution and position your business for future success. Let's create a comprehensive transformation strategy that drives growth, efficiency, and competitive advantage."
      ctaButtonText="Begin Your Transformation"
      ctaButtonLink="/contact"
    />
  );
}