import React from 'react';
import { ServicePageTemplate } from '@/components/templates/ServicePageTemplate';
import { Smartphone, Monitor, Brain, Users, Zap, BarChart3, <PERSON><PERSON><PERSON>, Layers } from 'lucide-react';

export default function IntelligentApplications() {
  return (
    <ServicePageTemplate
      // Hero Section
      heroTitle="Intelligent Applications"
      heroSubtitle="AI-Powered App Development"
      heroDescription="Create dynamic web and mobile applications that adapt intelligently to user behavior and preferences. Our intelligent applications learn from every interaction to deliver increasingly personalized and effective user experiences."
      heroImage="assets/images/botsolution.png"
      heroImageAlt="Intelligent applications interface demonstration"

      // Problem Statement Section
      problemTitle="The Static Application Problem"
      problemDescription="Traditional applications provide the same experience to every user, missing opportunities to engage, convert, and retain customers through personalization."
      problemPoints={[
        "One-size-fits-all interfaces that don't adapt to individual user preferences and behaviors",
        "Static content and features that become stale and fail to engage users over time",
        "Lack of intelligent recommendations leading to poor user experience and low conversion rates",
        "Inability to predict user needs or proactively solve problems before they arise",
        "Limited insights into user behavior patterns and application performance optimization",
        "High development costs for creating multiple versions to serve different user segments"
      ]}

      // Solution Section
      solutionTitle="Our Intelligent Applications"
      solutionDescription="We build applications that think, learn, and evolve with your users, creating dynamic experiences that improve engagement, satisfaction, and business outcomes."
      solutionFeatures={[
        {
          title: "Adaptive User Interfaces",
          description: "Interfaces that automatically adjust layout, content, and functionality based on user behavior, preferences, and context for optimal user experience.",
          icon: <Monitor className="w-6 h-6" />
        },
        {
          title: "Behavioral Learning",
          description: "AI systems that analyze user interactions to understand patterns, preferences, and needs, continuously improving the application experience.",
          icon: <Brain className="w-6 h-6" />
        },
        {
          title: "Personalized Content",
          description: "Dynamic content delivery that shows relevant information, products, and features to each user based on their profile and behavior history.",
          icon: <Users className="w-6 h-6" />
        },
        {
          title: "Predictive Features",
          description: "Intelligent features that anticipate user needs and provide proactive suggestions, recommendations, and assistance.",
          icon: <Sparkles className="w-6 h-6" />
        },
        {
          title: "Real-time Optimization",
          description: "Continuous performance monitoring and optimization that ensures your application delivers the best possible experience at all times.",
          icon: <Zap className="w-6 h-6" />
        },
        {
          title: "Cross-Platform Intelligence",
          description: "Seamless experience across web, mobile, and tablet platforms with synchronized learning and personalization.",
          icon: <Layers className="w-6 h-6" />
        }
      ]}

      // Proof/Evidence Section
      proofTitle="Application Performance Results"
      proofDescription="Our intelligent applications consistently outperform traditional static applications across key engagement and business metrics."
      proofItems={[
        {
          metric: "150%",
          description: "Increase in user engagement through personalized interfaces and intelligent content delivery",
          icon: <Users className="w-8 h-8" />
        },
        {
          metric: "80%",
          description: "Higher conversion rates from predictive features and personalized user experiences",
          icon: <BarChart3 className="w-8 h-8" />
        },
        {
          metric: "65%",
          description: "Reduction in user churn through adaptive interfaces that evolve with user needs",
          icon: <Smartphone className="w-8 h-8" />
        }
      ]}

      // CTA Section
      ctaTitle="Ready to Build Intelligent Applications?"
      ctaDescription="Transform your digital presence with applications that learn, adapt, and evolve with your users. Let's create intelligent solutions that drive engagement and deliver exceptional results."
      ctaButtonText="Start Your App Development"
      ctaButtonLink="/contact"
    />
  );
}