import React from 'react';
import { ServicePageTemplate } from '@/components/templates/ServicePageTemplate';
import { Link, Database, Workflow, RefreshCw, GitBranch, Shield, Gauge, Network } from 'lucide-react';

export default function SystemIntegration() {
  return (
    <ServicePageTemplate
      // Hero Section
      heroTitle="System Integration & Automation"
      heroSubtitle="Seamless AI Integration"
      heroDescription="Connect your AI systems seamlessly with existing business applications for automated workflow management. Our integration solutions eliminate data silos, streamline processes, and create unified digital ecosystems that work intelligently together."
      heroImage="assets/images/integration-background.png"
      heroImageAlt="System integration and automation workflow"

      // Problem Statement Section
      problemTitle="The Integration Challenge"
      problemDescription="Modern businesses use dozens of applications that don't communicate effectively, creating inefficiencies, data inconsistencies, and missed opportunities."
      problemPoints={[
        "Data trapped in isolated systems, preventing comprehensive business insights and decision-making",
        "Manual data entry between systems leading to errors, delays, and wasted employee time",
        "Inconsistent information across platforms causing customer confusion and internal conflicts",
        "Inability to automate complex workflows that span multiple applications and departments",
        "High costs of maintaining multiple disconnected systems and manual processes",
        "Security vulnerabilities from inconsistent access controls and data synchronization issues"
      ]}

      // Solution Section
      solutionTitle="Our Integration Solutions"
      solutionDescription="We create intelligent bridges between your applications, enabling seamless data flow and automated workflows that transform how your business operates."
      solutionFeatures={[
        {
          title: "Universal Connectivity",
          description: "Connect with 600+ business applications including CRMs, email platforms, databases, and enterprise software through robust APIs and integrations.",
          icon: <Network className="w-6 h-6" />
        },
        {
          title: "Intelligent Data Sync",
          description: "Real-time data synchronization with smart conflict resolution, ensuring information consistency across all your business systems.",
          icon: <RefreshCw className="w-6 h-6" />
        },
        {
          title: "Automated Workflows",
          description: "Create complex, multi-step workflows that trigger actions across different applications based on business rules and AI insights.",
          icon: <Workflow className="w-6 h-6" />
        },
        {
          title: "Database Integration",
          description: "Seamlessly connect to existing databases and data warehouses, enabling AI to access and analyze your complete business data.",
          icon: <Database className="w-6 h-6" />
        },
        {
          title: "Process Orchestration",
          description: "Coordinate complex business processes across multiple systems with intelligent routing, approvals, and exception handling.",
          icon: <GitBranch className="w-6 h-6" />
        },
        {
          title: "Enterprise Security",
          description: "Maintain security and compliance standards with encrypted connections, access controls, and audit trails for all integrations.",
          icon: <Shield className="w-6 h-6" />
        }
      ]}

      // Proof/Evidence Section
      proofTitle="Integration Success Stories"
      proofDescription="Our integration solutions have helped businesses eliminate inefficiencies, reduce costs, and unlock the full potential of their technology investments."
      proofItems={[
        {
          metric: "75%",
          description: "Reduction in manual data entry tasks through intelligent automation and synchronization",
          icon: <Gauge className="w-8 h-8" />
        },
        {
          metric: "90%",
          description: "Improvement in data accuracy across integrated systems, eliminating costly errors",
          icon: <Database className="w-8 h-8" />
        },
        {
          metric: "50%",
          description: "Faster business processes through automated workflows and seamless system communication",
          icon: <Workflow className="w-8 h-8" />
        }
      ]}

      // CTA Section
      ctaTitle="Ready to Connect Your Systems?"
      ctaDescription="Transform your disconnected applications into a unified, intelligent business ecosystem. Let's discuss how our integration solutions can streamline your operations and unlock new possibilities."
      ctaButtonText="Start Your Integration Journey"
      ctaButtonLink="/contact"
    />
  );
}